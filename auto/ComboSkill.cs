using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using auto;

public class ComboSkill : Form
{
    private Player player;
    private Player.ComboSkillInfo comboskill = new Player.ComboSkillInfo();
    
    private IContainer components = null;
    private GroupBox groupBox1;
    private ListBox listBoxSkills;
    private Button btnAddSkill;
    private Button btnRemoveSkill;
    private Button btnMoveUp;
    private Button btnMoveDown;
    private GroupBox groupBox2;
    private Label label1;
    private ComboBox cmbSkillName;
    private Label label2;
    private NumericUpDown numSkillId;
    private Label label3;
    private NumericUpDown numSkillDelay;
    private CheckBox chkSkillEnabled;
    private GroupBox groupBox3;
    private Label label4;
    private NumericUpDown numComboDelay;
    private CheckBox chkLoop;
    private CheckBox chkComboEnabled;
    private Button btnSave;
    private Button btnTest;

    // Danh sách skill phổ biến trong Jx2
    private Dictionary<string, uint> skillDatabase = new Dictionary<string, uint>
    {
        {"Thái <PERSON>", 1},
        {"Thiết Sa Chưởng", 2},
        {"Dư<PERSON>ng Chỉ", 3},
        {"Lục <PERSON>ạch Thần Kiếm", 4},
        {"Độc Long Chưởng", 5},
        {"Hàng Long Thập Bát Chưởng", 6},
        {"Thiên Sơn Lục Dương Chưởng", 7},
        {"Tiêu Dao Bộ", 8},
        {"Bắc <PERSON> Thần Công", 9},
        {"Cửu Âm Chân Kinh", 10},
        {"Dịch Cân Kinh", 11},
        {"Thiên Long Bát Bộ", 12},
        {"Phong Thần Cước", 13},
        {"Hỏa Diệm Đao", 14},
        {"Băng Phách Kiếm", 15},
        {"Lôi Điện Kiếm", 16},
        {"Thiên Địa Đồng Thọ", 17},
        {"Vạn Kiếm Quy Tông", 18},
        {"Cửu Thiên Huyền Nữ", 19},
        {"Thái Ất Chân Nhân", 20}
    };

    public ComboSkill(Client client)
    {
        InitializeComponent();
        player = client.player;
        
        // Load skill database vào combobox
        foreach (var skill in skillDatabase)
        {
            cmbSkillName.Items.Add(skill.Key);
        }
        
        if (client.IsChecked)
        {
            comboskill = player.ComboSkilllist;
            LoadComboSkillSettings();
        }
    }

    private void LoadComboSkillSettings()
    {
        chkComboEnabled.Checked = comboskill.IsEnabled;
        chkLoop.Checked = comboskill.IsLoop;
        numComboDelay.Value = comboskill.ComboDelay;
        
        listBoxSkills.Items.Clear();
        foreach (var skill in comboskill.Skills)
        {
            string displayText = $"{skill.SkillName} (ID: {skill.SkillId}, Delay: {skill.Delay}ms) - {(skill.IsEnabled ? "ON" : "OFF")}";
            listBoxSkills.Items.Add(displayText);
        }
    }

    private void BtnAddSkill_Click(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(cmbSkillName.Text))
        {
            MessageBox.Show("Vui lòng chọn tên skill!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var newSkill = new Player.SkillInfo
        {
            SkillName = cmbSkillName.Text,
            SkillId = (uint)numSkillId.Value,
            Delay = (int)numSkillDelay.Value,
            IsEnabled = chkSkillEnabled.Checked
        };

        comboskill.Skills.Add(newSkill);
        LoadComboSkillSettings();
        
        // Reset form
        cmbSkillName.SelectedIndex = -1;
        numSkillId.Value = 1;
        numSkillDelay.Value = 500;
        chkSkillEnabled.Checked = true;
    }

    private void BtnRemoveSkill_Click(object sender, EventArgs e)
    {
        if (listBoxSkills.SelectedIndex >= 0)
        {
            comboskill.Skills.RemoveAt(listBoxSkills.SelectedIndex);
            LoadComboSkillSettings();
        }
    }

    private void BtnMoveUp_Click(object sender, EventArgs e)
    {
        int selectedIndex = listBoxSkills.SelectedIndex;
        if (selectedIndex > 0)
        {
            var skill = comboskill.Skills[selectedIndex];
            comboskill.Skills.RemoveAt(selectedIndex);
            comboskill.Skills.Insert(selectedIndex - 1, skill);
            LoadComboSkillSettings();
            listBoxSkills.SelectedIndex = selectedIndex - 1;
        }
    }

    private void BtnMoveDown_Click(object sender, EventArgs e)
    {
        int selectedIndex = listBoxSkills.SelectedIndex;
        if (selectedIndex >= 0 && selectedIndex < comboskill.Skills.Count - 1)
        {
            var skill = comboskill.Skills[selectedIndex];
            comboskill.Skills.RemoveAt(selectedIndex);
            comboskill.Skills.Insert(selectedIndex + 1, skill);
            LoadComboSkillSettings();
            listBoxSkills.SelectedIndex = selectedIndex + 1;
        }
    }

    private void CmbSkillName_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (cmbSkillName.SelectedItem != null)
        {
            string skillName = cmbSkillName.SelectedItem.ToString();
            if (skillDatabase.ContainsKey(skillName))
            {
                numSkillId.Value = skillDatabase[skillName];
            }
        }
    }

    private void ChkComboEnabled_CheckedChanged(object sender, EventArgs e)
    {
        comboskill.IsEnabled = chkComboEnabled.Checked;
    }

    private void ChkLoop_CheckedChanged(object sender, EventArgs e)
    {
        comboskill.IsLoop = chkLoop.Checked;
    }

    private void NumComboDelay_ValueChanged(object sender, EventArgs e)
    {
        comboskill.ComboDelay = (int)numComboDelay.Value;
    }

    private void BtnSave_Click(object sender, EventArgs e)
    {
        player.ComboSkilllist = comboskill;
        MessageBox.Show("Đã lưu cấu hình combo skill!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        Close();
    }

    private void BtnTest_Click(object sender, EventArgs e)
    {
        if (comboskill.Skills.Count == 0)
        {
            MessageBox.Show("Chưa có skill nào trong combo!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        MessageBox.Show($"Test combo với {comboskill.Skills.Count} skill(s)\nDelay giữa combo: {comboskill.ComboDelay}ms\nLặp lại: {(comboskill.IsLoop ? "Có" : "Không")}", 
                       "Test Combo", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null)
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox1 = new GroupBox();
        listBoxSkills = new ListBox();
        btnAddSkill = new Button();
        btnRemoveSkill = new Button();
        btnMoveUp = new Button();
        btnMoveDown = new Button();
        groupBox2 = new GroupBox();
        label1 = new Label();
        cmbSkillName = new ComboBox();
        label2 = new Label();
        numSkillId = new NumericUpDown();
        label3 = new Label();
        numSkillDelay = new NumericUpDown();
        chkSkillEnabled = new CheckBox();
        groupBox3 = new GroupBox();
        label4 = new Label();
        numComboDelay = new NumericUpDown();
        chkLoop = new CheckBox();
        chkComboEnabled = new CheckBox();
        btnSave = new Button();
        btnTest = new Button();

        groupBox1.SuspendLayout();
        groupBox2.SuspendLayout();
        groupBox3.SuspendLayout();
        ((ISupportInitialize)numSkillId).BeginInit();
        ((ISupportInitialize)numSkillDelay).BeginInit();
        ((ISupportInitialize)numComboDelay).BeginInit();
        SuspendLayout();

        // groupBox1
        groupBox1.Controls.Add(btnMoveDown);
        groupBox1.Controls.Add(btnMoveUp);
        groupBox1.Controls.Add(btnRemoveSkill);
        groupBox1.Controls.Add(btnAddSkill);
        groupBox1.Controls.Add(listBoxSkills);
        groupBox1.Location = new Point(12, 12);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(300, 200);
        groupBox1.TabIndex = 0;
        groupBox1.TabStop = false;
        groupBox1.Text = "Danh sách Combo Skill";

        // listBoxSkills
        listBoxSkills.FormattingEnabled = true;
        listBoxSkills.Location = new Point(6, 19);
        listBoxSkills.Name = "listBoxSkills";
        listBoxSkills.Size = new Size(288, 134);
        listBoxSkills.TabIndex = 0;

        // btnAddSkill
        btnAddSkill.Location = new Point(6, 159);
        btnAddSkill.Name = "btnAddSkill";
        btnAddSkill.Size = new Size(60, 23);
        btnAddSkill.TabIndex = 1;
        btnAddSkill.Text = "Thêm";
        btnAddSkill.UseVisualStyleBackColor = true;
        btnAddSkill.Click += BtnAddSkill_Click;

        // btnRemoveSkill
        btnRemoveSkill.Location = new Point(72, 159);
        btnRemoveSkill.Name = "btnRemoveSkill";
        btnRemoveSkill.Size = new Size(60, 23);
        btnRemoveSkill.TabIndex = 2;
        btnRemoveSkill.Text = "Xóa";
        btnRemoveSkill.UseVisualStyleBackColor = true;
        btnRemoveSkill.Click += BtnRemoveSkill_Click;

        // btnMoveUp
        btnMoveUp.Location = new Point(200, 159);
        btnMoveUp.Name = "btnMoveUp";
        btnMoveUp.Size = new Size(40, 23);
        btnMoveUp.TabIndex = 3;
        btnMoveUp.Text = "↑";
        btnMoveUp.UseVisualStyleBackColor = true;
        btnMoveUp.Click += BtnMoveUp_Click;

        // btnMoveDown
        btnMoveDown.Location = new Point(246, 159);
        btnMoveDown.Name = "btnMoveDown";
        btnMoveDown.Size = new Size(40, 23);
        btnMoveDown.TabIndex = 4;
        btnMoveDown.Text = "↓";
        btnMoveDown.UseVisualStyleBackColor = true;
        btnMoveDown.Click += BtnMoveDown_Click;

        // groupBox2
        groupBox2.Controls.Add(chkSkillEnabled);
        groupBox2.Controls.Add(numSkillDelay);
        groupBox2.Controls.Add(label3);
        groupBox2.Controls.Add(numSkillId);
        groupBox2.Controls.Add(label2);
        groupBox2.Controls.Add(cmbSkillName);
        groupBox2.Controls.Add(label1);
        groupBox2.Location = new Point(318, 12);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(250, 120);
        groupBox2.TabIndex = 1;
        groupBox2.TabStop = false;
        groupBox2.Text = "Thêm Skill";

        // label1
        label1.AutoSize = true;
        label1.Location = new Point(6, 22);
        label1.Name = "label1";
        label1.Size = new Size(55, 13);
        label1.TabIndex = 0;
        label1.Text = "Tên Skill:";

        // cmbSkillName
        cmbSkillName.DropDownStyle = ComboBoxStyle.DropDownList;
        cmbSkillName.FormattingEnabled = true;
        cmbSkillName.Location = new Point(70, 19);
        cmbSkillName.Name = "cmbSkillName";
        cmbSkillName.Size = new Size(174, 21);
        cmbSkillName.TabIndex = 1;
        cmbSkillName.SelectedIndexChanged += CmbSkillName_SelectedIndexChanged;

        // label2
        label2.AutoSize = true;
        label2.Location = new Point(6, 49);
        label2.Name = "label2";
        label2.Size = new Size(47, 13);
        label2.TabIndex = 2;
        label2.Text = "Skill ID:";

        // numSkillId
        numSkillId.Location = new Point(70, 46);
        numSkillId.Maximum = new decimal(new int[] { 9999, 0, 0, 0 });
        numSkillId.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
        numSkillId.Name = "numSkillId";
        numSkillId.Size = new Size(80, 20);
        numSkillId.TabIndex = 3;
        numSkillId.Value = new decimal(new int[] { 1, 0, 0, 0 });

        // label3
        label3.AutoSize = true;
        label3.Location = new Point(6, 75);
        label3.Name = "label3";
        label3.Size = new Size(58, 13);
        label3.TabIndex = 4;
        label3.Text = "Delay (ms):";

        // numSkillDelay
        numSkillDelay.Increment = new decimal(new int[] { 50, 0, 0, 0 });
        numSkillDelay.Location = new Point(70, 72);
        numSkillDelay.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
        numSkillDelay.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
        numSkillDelay.Name = "numSkillDelay";
        numSkillDelay.Size = new Size(80, 20);
        numSkillDelay.TabIndex = 5;
        numSkillDelay.Value = new decimal(new int[] { 500, 0, 0, 0 });

        // chkSkillEnabled
        chkSkillEnabled.AutoSize = true;
        chkSkillEnabled.Checked = true;
        chkSkillEnabled.CheckState = CheckState.Checked;
        chkSkillEnabled.Location = new Point(156, 74);
        chkSkillEnabled.Name = "chkSkillEnabled";
        chkSkillEnabled.Size = new Size(88, 17);
        chkSkillEnabled.TabIndex = 6;
        chkSkillEnabled.Text = "Kích hoạt";
        chkSkillEnabled.UseVisualStyleBackColor = true;

        // groupBox3
        groupBox3.Controls.Add(chkComboEnabled);
        groupBox3.Controls.Add(chkLoop);
        groupBox3.Controls.Add(numComboDelay);
        groupBox3.Controls.Add(label4);
        groupBox3.Location = new Point(318, 138);
        groupBox3.Name = "groupBox3";
        groupBox3.Size = new Size(250, 74);
        groupBox3.TabIndex = 2;
        groupBox3.TabStop = false;
        groupBox3.Text = "Cấu hình Combo";

        // label4
        label4.AutoSize = true;
        label4.Location = new Point(6, 22);
        label4.Name = "label4";
        label4.Size = new Size(88, 13);
        label4.TabIndex = 0;
        label4.Text = "Delay Combo (ms):";

        // numComboDelay
        numComboDelay.Increment = new decimal(new int[] { 100, 0, 0, 0 });
        numComboDelay.Location = new Point(100, 19);
        numComboDelay.Maximum = new decimal(new int[] { 30000, 0, 0, 0 });
        numComboDelay.Minimum = new decimal(new int[] { 500, 0, 0, 0 });
        numComboDelay.Name = "numComboDelay";
        numComboDelay.Size = new Size(80, 20);
        numComboDelay.TabIndex = 1;
        numComboDelay.Value = new decimal(new int[] { 1000, 0, 0, 0 });
        numComboDelay.ValueChanged += NumComboDelay_ValueChanged;

        // chkLoop
        chkLoop.AutoSize = true;
        chkLoop.Checked = true;
        chkLoop.CheckState = CheckState.Checked;
        chkLoop.Location = new Point(6, 45);
        chkLoop.Name = "chkLoop";
        chkLoop.Size = new Size(66, 17);
        chkLoop.TabIndex = 2;
        chkLoop.Text = "Lặp lại";
        chkLoop.UseVisualStyleBackColor = true;
        chkLoop.CheckedChanged += ChkLoop_CheckedChanged;

        // chkComboEnabled
        chkComboEnabled.AutoSize = true;
        chkComboEnabled.Location = new Point(100, 45);
        chkComboEnabled.Name = "chkComboEnabled";
        chkComboEnabled.Size = new Size(88, 17);
        chkComboEnabled.TabIndex = 3;
        chkComboEnabled.Text = "Bật Combo";
        chkComboEnabled.UseVisualStyleBackColor = true;
        chkComboEnabled.CheckedChanged += ChkComboEnabled_CheckedChanged;

        // btnSave
        btnSave.Location = new Point(400, 218);
        btnSave.Name = "btnSave";
        btnSave.Size = new Size(75, 23);
        btnSave.TabIndex = 3;
        btnSave.Text = "Lưu";
        btnSave.UseVisualStyleBackColor = true;
        btnSave.Click += BtnSave_Click;

        // btnTest
        btnTest.Location = new Point(481, 218);
        btnTest.Name = "btnTest";
        btnTest.Size = new Size(75, 23);
        btnTest.TabIndex = 4;
        btnTest.Text = "Test";
        btnTest.UseVisualStyleBackColor = true;
        btnTest.Click += BtnTest_Click;

        // ComboSkill
        AutoScaleDimensions = new SizeF(6F, 13F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(580, 253);
        Controls.Add(btnTest);
        Controls.Add(btnSave);
        Controls.Add(groupBox3);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;
        MinimizeBox = false;
        Name = "ComboSkill";
        StartPosition = FormStartPosition.CenterParent;
        Text = "Auto Combo Skill";

        groupBox1.ResumeLayout(false);
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        groupBox3.ResumeLayout(false);
        groupBox3.PerformLayout();
        ((ISupportInitialize)numSkillId).EndInit();
        ((ISupportInitialize)numSkillDelay).EndInit();
        ((ISupportInitialize)numComboDelay).EndInit();
        ResumeLayout(false);
    }
}
