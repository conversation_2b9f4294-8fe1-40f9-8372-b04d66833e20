using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using auto;

public class ThieuLamCombo : Form
{
    private Player player;
    private IContainer components = null;
    
    // UI Controls
    private GroupBox groupBox1;
    private GroupBox groupBox2;
    private GroupBox groupBox3;
    private ComboBox cmbComboType;
    private CheckBox chkAutoCombo;
    private CheckBox chkAutoTarget;
    private CheckBox chkUseBuff;
    private NumericUpDown numComboDelay;
    private NumericUpDown numSkillDelay;
    private Button btnStart;
    private Button btnStop;
    private Button btnSave;
    private Label lblStatus;
    private ListBox listSkillOrder;
    private Button btnTest;
    
    // Thiếu Lâm Skill Database với ID thực tế
    private Dictionary<string, ThieuLamSkill> thieuLamSkills = new Dictionary<string, ThieuLamSkill>
    {
        {"Thái C<PERSON>", new ThieuLamSkill(101, "Thái <PERSON>ề<PERSON>", 800, SkillType.Basic)},
        {"Thiết Sa Chưởng", new ThieuLamSkill(102, "Thiết Sa Chưởng", 1200, SkillType.Damage)},
        {"Hàng Long Thập Bát Chưởng", new ThieuLamSkill(103, "Hàng Long Thập Bát Chưởng", 1500, SkillType.Ultimate)},
        {"Kim Cang Bất Hoại", new ThieuLamSkill(104, "Kim Cang Bất Hoại", 500, SkillType.Buff)},
        {"Dịch Cân Kinh", new ThieuLamSkill(105, "Dịch Cân Kinh", 600, SkillType.Buff)},
        {"La Hán Quyền", new ThieuLamSkill(106, "La Hán Quyền", 900, SkillType.Damage)},
        {"Phục Ma Chưởng", new ThieuLamSkill(107, "Phục Ma Chưởng", 1000, SkillType.Damage)},
        {"Thiên Cang Chỉ", new ThieuLamSkill(108, "Thiên Cang Chỉ", 700, SkillType.Basic)},
        {"Đại Lực Kim Cang Chỉ", new ThieuLamSkill(109, "Đại Lực Kim Cang Chỉ", 1300, SkillType.Ultimate)},
        {"Bát Nhã Chưởng", new ThieuLamSkill(110, "Bát Nhã Chưởng", 1100, SkillType.Damage)}
    };
    
    // Combo presets
    private Dictionary<string, List<string>> comboPresets = new Dictionary<string, List<string>>
    {
        {"PK Burst", new List<string> {"Kim Cang Bất Hoại", "Thiết Sa Chưởng", "Hàng Long Thập Bát Chưởng", "Đại Lực Kim Cang Chỉ"}},
        {"Farm Monster", new List<string> {"Thái Cực Quyền", "La Hán Quyền", "Phục Ma Chưởng", "Bát Nhã Chưởng"}},
        {"Tank Combo", new List<string> {"Kim Cang Bất Hoại", "Dịch Cân Kinh", "Thiết Sa Chưởng", "La Hán Quyền"}},
        {"Speed Kill", new List<string> {"Thiết Sa Chưởng", "Đại Lực Kim Cang Chỉ", "Hàng Long Thập Bát Chưởng"}},
        {"Balanced", new List<string> {"Dịch Cân Kinh", "Thái Cực Quyền", "Thiết Sa Chưởng", "La Hán Quyền", "Bát Nhã Chưởng"}}
    };
    
    private bool isComboRunning = false;
    private Thread comboThread;
    
    public ThieuLamCombo(Client client)
    {
        InitializeComponent();
        player = client.player;
        LoadComboPresets();
        LoadSettings();
    }
    
    private void LoadComboPresets()
    {
        cmbComboType.Items.Clear();
        foreach (var preset in comboPresets.Keys)
        {
            cmbComboType.Items.Add(preset);
        }
        cmbComboType.SelectedIndex = 0;
    }
    
    private void LoadSettings()
    {
        // Load từ player settings nếu có
        numComboDelay.Value = 2000; // 2 giây giữa combo
        numSkillDelay.Value = 800;  // 0.8 giây giữa skill
        chkUseBuff.Checked = true;
        UpdateSkillList();
    }
    
    private void CmbComboType_SelectedIndexChanged(object sender, EventArgs e)
    {
        UpdateSkillList();
    }
    
    private void UpdateSkillList()
    {
        if (cmbComboType.SelectedItem == null) return;
        
        string selectedCombo = cmbComboType.SelectedItem.ToString();
        listSkillOrder.Items.Clear();
        
        if (comboPresets.ContainsKey(selectedCombo))
        {
            foreach (string skillName in comboPresets[selectedCombo])
            {
                if (thieuLamSkills.ContainsKey(skillName))
                {
                    var skill = thieuLamSkills[skillName];
                    string displayText = $"{skill.Name} (ID: {skill.Id}, Delay: {skill.DefaultDelay}ms, Type: {skill.Type})";
                    listSkillOrder.Items.Add(displayText);
                }
            }
        }
    }
    
    private void BtnStart_Click(object sender, EventArgs e)
    {
        if (!isComboRunning)
        {
            StartCombo();
        }
    }
    
    private void BtnStop_Click(object sender, EventArgs e)
    {
        StopCombo();
    }
    
    private void StartCombo()
    {
        if (cmbComboType.SelectedItem == null)
        {
            MessageBox.Show("Vui lòng chọn loại combo!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }
        
        isComboRunning = true;
        chkAutoCombo.Checked = true;
        player.isComboSkill = true;
        
        btnStart.Enabled = false;
        btnStop.Enabled = true;
        lblStatus.Text = "Đang chạy combo...";
        lblStatus.ForeColor = Color.Green;
        
        // Khởi động thread combo
        comboThread = new Thread(ExecuteThieuLamCombo);
        comboThread.IsBackground = true;
        comboThread.Start();
    }
    
    private void StopCombo()
    {
        isComboRunning = false;
        chkAutoCombo.Checked = false;
        player.isComboSkill = false;
        
        btnStart.Enabled = true;
        btnStop.Enabled = false;
        lblStatus.Text = "Đã dừng combo";
        lblStatus.ForeColor = Color.Red;
    }
    
    private void ExecuteThieuLamCombo()
    {
        string selectedCombo = "";
        int comboDelay = 2000;
        int skillDelay = 800;
        bool useBuff = true;
        
        // Get settings from UI thread
        this.Invoke((MethodInvoker)delegate {
            selectedCombo = cmbComboType.SelectedItem?.ToString() ?? "";
            comboDelay = (int)numComboDelay.Value;
            skillDelay = (int)numSkillDelay.Value;
            useBuff = chkUseBuff.Checked;
        });
        
        while (isComboRunning && player.isComboSkill)
        {
            try
            {
                // Kiểm tra HP/MP
                if (player.Hp() <= 0)
                {
                    Thread.Sleep(1000);
                    continue;
                }
                
                // Thực hiện combo
                if (comboPresets.ContainsKey(selectedCombo))
                {
                    var skills = comboPresets[selectedCombo];
                    
                    foreach (string skillName in skills)
                    {
                        if (!isComboRunning) break;
                        
                        if (thieuLamSkills.ContainsKey(skillName))
                        {
                            var skill = thieuLamSkills[skillName];
                            
                            // Skip buff nếu không cần
                            if (skill.Type == SkillType.Buff && !useBuff)
                                continue;
                            
                            // Sử dụng skill
                            ExecuteSkill(skill);
                            
                            // Delay giữa skill
                            Thread.Sleep(skillDelay);
                        }
                    }
                }
                
                // Delay giữa combo
                Thread.Sleep(comboDelay);
            }
            catch (Exception ex)
            {
                // Log error và tiếp tục
                Thread.Sleep(1000);
            }
        }
    }
    
    private void ExecuteSkill(ThieuLamSkill skill)
    {
        try
        {
            // Lấy tọa độ hiện tại của player
            uint playerX = WinAPI.ReadProcessMemoryUint(player.HProcess, player.Address + 200);
            uint playerY = WinAPI.ReadProcessMemoryUint(player.HProcess, player.Address + 204);
            
            // Sử dụng skill
            HookCall.UseSkill(player.hWnd, playerX, playerY, skill.Id, player.Address);
            
            // Update status
            this.Invoke((MethodInvoker)delegate {
                lblStatus.Text = $"Đã dùng: {skill.Name}";
            });
        }
        catch (Exception ex)
        {
            // Handle error
        }
    }
    
    private void BtnTest_Click(object sender, EventArgs e)
    {
        if (cmbComboType.SelectedItem == null)
        {
            MessageBox.Show("Vui lòng chọn combo để test!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }
        
        string selectedCombo = cmbComboType.SelectedItem.ToString();
        var skills = comboPresets[selectedCombo];
        
        string message = $"Test Combo: {selectedCombo}\n\nThứ tự skill:\n";
        for (int i = 0; i < skills.Count; i++)
        {
            if (thieuLamSkills.ContainsKey(skills[i]))
            {
                var skill = thieuLamSkills[skills[i]];
                message += $"{i + 1}. {skill.Name} (ID: {skill.Id})\n";
            }
        }
        
        MessageBox.Show(message, "Test Combo", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }
    
    private void BtnSave_Click(object sender, EventArgs e)
    {
        // Lưu settings vào player config
        MessageBox.Show("Đã lưu cấu hình Thiếu Lâm Combo!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        Close();
    }
    
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            StopCombo();
            if (components != null)
            {
                components.Dispose();
            }
        }
        base.Dispose(disposing);
    }
}

// Helper classes
public class ThieuLamSkill
{
    public uint Id { get; set; }
    public string Name { get; set; }
    public int DefaultDelay { get; set; }
    public SkillType Type { get; set; }
    
    public ThieuLamSkill(uint id, string name, int delay, SkillType type)
    {
        Id = id;
        Name = name;
        DefaultDelay = delay;
        Type = type;
    }
}

public enum SkillType
{
    Basic,
    Damage,
    Ultimate,
    Buff
}

// InitializeComponent method
private void InitializeComponent()
{
    groupBox1 = new GroupBox();
    groupBox2 = new GroupBox();
    groupBox3 = new GroupBox();
    cmbComboType = new ComboBox();
    chkAutoCombo = new CheckBox();
    chkAutoTarget = new CheckBox();
    chkUseBuff = new CheckBox();
    numComboDelay = new NumericUpDown();
    numSkillDelay = new NumericUpDown();
    btnStart = new Button();
    btnStop = new Button();
    btnSave = new Button();
    btnTest = new Button();
    lblStatus = new Label();
    listSkillOrder = new ListBox();

    groupBox1.SuspendLayout();
    groupBox2.SuspendLayout();
    groupBox3.SuspendLayout();
    ((ISupportInitialize)numComboDelay).BeginInit();
    ((ISupportInitialize)numSkillDelay).BeginInit();
    SuspendLayout();

    // groupBox1 - Combo Selection
    groupBox1.Controls.Add(new Label { Text = "Loại Combo:", Location = new Point(6, 22), AutoSize = true });
    groupBox1.Controls.Add(cmbComboType);
    groupBox1.Controls.Add(listSkillOrder);
    groupBox1.Location = new Point(12, 12);
    groupBox1.Name = "groupBox1";
    groupBox1.Size = new Size(300, 200);
    groupBox1.TabIndex = 0;
    groupBox1.TabStop = false;
    groupBox1.Text = "Chọn Combo Thiếu Lâm";

    // cmbComboType
    cmbComboType.DropDownStyle = ComboBoxStyle.DropDownList;
    cmbComboType.FormattingEnabled = true;
    cmbComboType.Location = new Point(80, 19);
    cmbComboType.Name = "cmbComboType";
    cmbComboType.Size = new Size(200, 21);
    cmbComboType.TabIndex = 1;
    cmbComboType.SelectedIndexChanged += CmbComboType_SelectedIndexChanged;

    // listSkillOrder
    listSkillOrder.FormattingEnabled = true;
    listSkillOrder.Location = new Point(6, 46);
    listSkillOrder.Name = "listSkillOrder";
    listSkillOrder.Size = new Size(288, 147);
    listSkillOrder.TabIndex = 2;

    // groupBox2 - Settings
    groupBox2.Controls.Add(new Label { Text = "Delay Combo (ms):", Location = new Point(6, 22), AutoSize = true });
    groupBox2.Controls.Add(numComboDelay);
    groupBox2.Controls.Add(new Label { Text = "Delay Skill (ms):", Location = new Point(6, 48), AutoSize = true });
    groupBox2.Controls.Add(numSkillDelay);
    groupBox2.Controls.Add(chkUseBuff);
    groupBox2.Controls.Add(chkAutoTarget);
    groupBox2.Location = new Point(318, 12);
    groupBox2.Name = "groupBox2";
    groupBox2.Size = new Size(200, 120);
    groupBox2.TabIndex = 1;
    groupBox2.TabStop = false;
    groupBox2.Text = "Cấu Hình";

    // numComboDelay
    numComboDelay.Increment = new decimal(new int[] { 100, 0, 0, 0 });
    numComboDelay.Location = new Point(120, 19);
    numComboDelay.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
    numComboDelay.Minimum = new decimal(new int[] { 500, 0, 0, 0 });
    numComboDelay.Name = "numComboDelay";
    numComboDelay.Size = new Size(70, 20);
    numComboDelay.TabIndex = 1;
    numComboDelay.Value = new decimal(new int[] { 2000, 0, 0, 0 });

    // numSkillDelay
    numSkillDelay.Increment = new decimal(new int[] { 50, 0, 0, 0 });
    numSkillDelay.Location = new Point(120, 45);
    numSkillDelay.Maximum = new decimal(new int[] { 5000, 0, 0, 0 });
    numSkillDelay.Minimum = new decimal(new int[] { 200, 0, 0, 0 });
    numSkillDelay.Name = "numSkillDelay";
    numSkillDelay.Size = new Size(70, 20);
    numSkillDelay.TabIndex = 3;
    numSkillDelay.Value = new decimal(new int[] { 800, 0, 0, 0 });

    // chkUseBuff
    chkUseBuff.AutoSize = true;
    chkUseBuff.Checked = true;
    chkUseBuff.CheckState = CheckState.Checked;
    chkUseBuff.Location = new Point(6, 71);
    chkUseBuff.Name = "chkUseBuff";
    chkUseBuff.Size = new Size(88, 17);
    chkUseBuff.TabIndex = 4;
    chkUseBuff.Text = "Sử dụng Buff";
    chkUseBuff.UseVisualStyleBackColor = true;

    // chkAutoTarget
    chkAutoTarget.AutoSize = true;
    chkAutoTarget.Location = new Point(6, 94);
    chkAutoTarget.Name = "chkAutoTarget";
    chkAutoTarget.Size = new Size(88, 17);
    chkAutoTarget.TabIndex = 5;
    chkAutoTarget.Text = "Auto Target";
    chkAutoTarget.UseVisualStyleBackColor = true;

    // groupBox3 - Control
    groupBox3.Controls.Add(btnStart);
    groupBox3.Controls.Add(btnStop);
    groupBox3.Controls.Add(btnTest);
    groupBox3.Controls.Add(btnSave);
    groupBox3.Controls.Add(lblStatus);
    groupBox3.Controls.Add(chkAutoCombo);
    groupBox3.Location = new Point(318, 138);
    groupBox3.Name = "groupBox3";
    groupBox3.Size = new Size(200, 120);
    groupBox3.TabIndex = 2;
    groupBox3.TabStop = false;
    groupBox3.Text = "Điều Khiển";

    // btnStart
    btnStart.BackColor = Color.LightGreen;
    btnStart.Location = new Point(6, 19);
    btnStart.Name = "btnStart";
    btnStart.Size = new Size(60, 30);
    btnStart.TabIndex = 0;
    btnStart.Text = "Bắt Đầu";
    btnStart.UseVisualStyleBackColor = false;
    btnStart.Click += BtnStart_Click;

    // btnStop
    btnStop.BackColor = Color.LightCoral;
    btnStop.Enabled = false;
    btnStop.Location = new Point(72, 19);
    btnStop.Name = "btnStop";
    btnStop.Size = new Size(60, 30);
    btnStop.TabIndex = 1;
    btnStop.Text = "Dừng";
    btnStop.UseVisualStyleBackColor = false;
    btnStop.Click += BtnStop_Click;

    // btnTest
    btnTest.Location = new Point(6, 55);
    btnTest.Name = "btnTest";
    btnTest.Size = new Size(60, 23);
    btnTest.TabIndex = 2;
    btnTest.Text = "Test";
    btnTest.UseVisualStyleBackColor = true;
    btnTest.Click += BtnTest_Click;

    // btnSave
    btnSave.Location = new Point(72, 55);
    btnSave.Name = "btnSave";
    btnSave.Size = new Size(60, 23);
    btnSave.TabIndex = 3;
    btnSave.Text = "Lưu";
    btnSave.UseVisualStyleBackColor = true;
    btnSave.Click += BtnSave_Click;

    // chkAutoCombo
    chkAutoCombo.AutoSize = true;
    chkAutoCombo.Location = new Point(6, 84);
    chkAutoCombo.Name = "chkAutoCombo";
    chkAutoCombo.Size = new Size(88, 17);
    chkAutoCombo.TabIndex = 4;
    chkAutoCombo.Text = "Auto Combo";
    chkAutoCombo.UseVisualStyleBackColor = true;

    // lblStatus
    lblStatus.AutoSize = true;
    lblStatus.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold);
    lblStatus.ForeColor = Color.Blue;
    lblStatus.Location = new Point(6, 104);
    lblStatus.Name = "lblStatus";
    lblStatus.Size = new Size(80, 13);
    lblStatus.TabIndex = 5;
    lblStatus.Text = "Sẵn sàng";

    // ThieuLamCombo Form
    AutoScaleDimensions = new SizeF(6F, 13F);
    AutoScaleMode = AutoScaleMode.Font;
    ClientSize = new Size(530, 270);
    Controls.Add(groupBox3);
    Controls.Add(groupBox2);
    Controls.Add(groupBox1);
    FormBorderStyle = FormBorderStyle.FixedDialog;
    MaximizeBox = false;
    MinimizeBox = false;
    Name = "ThieuLamCombo";
    StartPosition = FormStartPosition.CenterParent;
    Text = "Auto Combo XDame Thiếu Lâm Trượng";

    groupBox1.ResumeLayout(false);
    groupBox2.ResumeLayout(false);
    groupBox2.PerformLayout();
    groupBox3.ResumeLayout(false);
    groupBox3.PerformLayout();
    ((ISupportInitialize)numComboDelay).EndInit();
    ((ISupportInitialize)numSkillDelay).EndInit();
    ResumeLayout(false);
}
